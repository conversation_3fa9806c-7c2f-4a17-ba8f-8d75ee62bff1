export {};
/**
 * Prefix 'Bee' signifies that the type is 'complete'
 * ie, exists in camelcase & is typed with booleans instead of boolean strings
 */
declare global {
  // **********************
  // Nectar old pascal case entities.
  // TODO: Rewrite it to camel case and remove.

  interface Alerts {
    alerts_new_yard_created_mail: boolean;
    alerts_new_yard_created_sms: boolean;
    alerts_hive_moved_mail: boolean;
    alerts_hive_moved_sms: boolean;
    alerts_hive_status_mail: boolean;
    alerts_hive_status_sms: boolean;
    alerts_daily_report_mail: boolean;
  }

  interface User extends Alerts {
    dnd_end: string | any;
    dnd_start: string | any;
    email: string;
    event_read_on: string | null;
    feature_hht_manager: boolean;
    feature_hht_worker: boolean;
    feature_sensor: boolean;
    first_name: string;
    hs_threshold: number;
    is_active: boolean;
    is_staff: boolean;
    language_code: 'EN' | 'ES' | 'FR' | string;
    last_name: string;
    notify_by_email: boolean;
    notify_by_phone: boolean;
    operation_id: number;
    operation_name: string;
    phone_number: string;
    recap_daily: boolean;
    recap_weekly: boolean;
    season_start: string;
    temp_unit: 'C' | 'F' | string;
    timezone: 'UTC' | string;
    username: string;
  }

  type BeeYardStatus = 'active' | 'inactive' | 'unloaded' | 'emptied';
  type YardType = 'pollination' | 'honey' | 'holding' | 'indoor wintering' | 'outdoor wintering' | 'mating';

  interface Yard {
    created_at: string;
    crop_type: string | null;
    group_ids: any[];
    id: number;
    is_new: boolean;
    last_feeding_action: number;
    last_feeding_datetime: string;
    last_feeding_nb_hives: number;
    last_inspection: any;
    last_inspection_datetime: string;
    last_inspection_workername: string;
    last_treatment_action: number;
    last_treatment_datetime: string;
    last_treatment_nb_hives: number;
    latest_feedings: any[];
    latest_treatments: any[];
    avg_visits: number;
    name: string;
    contract_name: string;
    nb_deadout_season: number;
    nb_hives: number;
    nb_medium_hives: number;
    nb_queenless_hives: number;
    nb_strong_hives: number;
    nb_weak_hives: number;
    nearest_location: string;
    requiring_attention: boolean;
    status: BeeYardStatus;
    yard_type: any;
  }

  interface BeeHealthScore {
    healthScore: number | null;
    usedNbHives: number;
    totalNbHives: number;
  }

  interface BeeGlobalSearchYard {
    id: number;
    name: string;
    nbHives: number;
    contractName: string;
  }

  interface BeeYardsListYard {
    avgVisits: number;

    createdAt: string; //  '2022-05-11T19:55:32.296558Z';
    cropTypesIds: number[];
    contractName: string | null;

    groupIds: number[];

    id: number;
    isNew: boolean;

    name: string;

    nbDeadoutSeason: number;
    nbHives: number;
    nbWeakHives: number;
    nbMediumHives: number;
    nbStrongHives: number;
    nbQueenlessHives: number;
    nearestLocation: string;

    latestTreatments: BeeYardExecutedAction[];
    latestFeedings: BeeYardExecutedAction[];

    lastInspection: BeeYardInspection;
    lastInspectionDatetime: string; //  '2022-09-29 21:46:36.160584+00:00';
    lastInspectionWorkername: string;
    lastFeedingDatetime: string; // '2022-09-29 21:46:36.160584+00:00';
    lastFeedingAction: number;
    lastFeedingNbHives: number;
    lastTreatmentDatetime: string; //'2022-09-01 23:11:04.071552+00:00';
    lastTreatmentAction: number;
    lastTreatmentNbHives: number;

    requiringAttention: boolean;

    status: BeeYardStatus;

    targetHiveNumber: number | null;

    yardType: YardType;
  }

  // ***************
  // Nectar entities

  interface BeeUser {
    id: number;

    username: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    isStaff: boolean;

    /** Special settings for the Intercom integration. */
    intercomSettings: {
      userHash: string;
      userId: string;
    };

    /** It can be null if the current operation was not chosen yet. */
    currentMembership: BeeMembership<{ seasonStart: string }> | null;
    memberships: Array<BeeMembership<{ hiveCount: number }>>;

    userPreferences: BeeUserPreferences;
    membershipPreferences: BeeMembershipPreferences;
  }

  interface BeeMembership<OE> {
    isManager: boolean;
    operation: {
      id: number;
      name: string;
      color: string;
    } & OE;
  }

  interface BeeUserPreferences {
    timezone: string;
    languageCode: string;
  }

  interface BeeMembershipPreferences {
    settingsPracticesHideDisabled: boolean;
    alertsNewYardCreatedSms: boolean;
    alertsNewYardCreatedMail: boolean;
    alertsHiveMovedSms: boolean;
    alertsHiveMovedMail: boolean;
    alertsHiveStatusSms: boolean;
    alertsHiveStatusMail: boolean;
    alertsDailyReportMail: boolean;
    alertsYardReportMail: boolean;
  }

  interface BeePermissions {
    names: string[];
    role: 'manager' | 'worker' | 'sensor' | string;
  }

  type BeePracticeDefinition = {
    id: number;
    name: {
      EN: string;
      ES?: string;
    };
    availableInHiveReports: boolean;
    availableInYardReports: boolean;
    icon: string;
    flag: string;
    isActive: boolean;
    isDeleted: boolean;
    isHiveData: boolean;
    actions: Array<BeeAction>;
  } & (
    | {
        fieldType: 'system' | 'radio' | 'checkbox';
      }
    | {
        fieldType: 'slider';
        config: {
          slider: {
            max: number;
            min: number;
            step: number;
          };
        };
      }
  );

  interface BeeAction {
    id: number;
    name: {
      EN: string;
      ES?: string;
    };
    flag: string;
    isActive: boolean;
    isDeleted: boolean;
  }

  interface BeeOperation {
    id: number;
    name: string;
    color: string;
    seasonStart: Date;
    preferences: BeeOperationPreferences;
    releaseFlags: BeeReleaseFlags;
  }

  interface BeeReleaseFlags {
    CYCLES: boolean;
  }

  interface LastVisitMaxDays {
    fresh: number;
    stale: number;
    outdated: number;
    inapplicable: number;
  }

  interface BeeOperationPreferences extends BeeOperationPermissions {
    /**
     * Hive structure.
     * */
    framesPerHoneySupers_Ten?: boolean;
    framesPerHoneySupers_Nine?: boolean;
    framesPerHoneySupers_Eight?: boolean;
    framesPerHoneySupers_Other?: boolean;
    frameBase_Plastic?: boolean;
    frameBase_Wax?: boolean;
    frameBase_Other?: boolean;
    honeySuperType_Shallow?: boolean;
    honeySuperType_Medium?: boolean;
    honeySuperType_Deep?: boolean;
    honeySuperType_Other?: boolean;
    broodBoxes_One?: boolean;
    broodBoxes_Two?: boolean;
    broodBoxes_Three?: boolean;
    broodBoxes_Other?: boolean;
    bottomBoards_Solid?: boolean;
    bottomBoards_Screen?: boolean;
    bottomBoards_Other?: boolean;
    queenExcluder_Metal?: boolean;
    queenExcluder_Plastic?: boolean;
    queenExcluder_None?: boolean;

    /** Whiteboard state. */
    whiteboardState?: any;

    /** Operation settings */
    operationSettingsShowDisabledPractices?: boolean;

    /** */
    lastVisitMaxDays: LastVisitMaxDays;

    insights?: BeeInsightsPreferences;

    featurePlanningPermission?: BeePermissionFlagValue;
    featureInsightsPermission?: BeePermissionFlagValue;
    featurePollinationPermission?: BeePermissionFlagValue;
  }

  interface BeeOperationPermissions {
    [key: BeePermissionFlagKey]: BeePermissionFlagValue;
  }
  type BeePermissionFlagKey =
    | 'featurePlanningPermission'
    | 'featureInsightsPermission'
    | 'featurePollinationPermission';
  type BeePermissionFlagValue = 'ENABLED' | 'DISABLED' | 'PAYWALL';

  interface BeeCropType {
    id: number;
    name: string;
  }

  interface BeeGroup {
    id: number;
    color: string;
    name: string;
  }
  // Removed hives are not currently displayed in the MP. They can be treated like retired hives.
  type HiveStatus = 'introduced' | 'queenright' | 'queenless' | 'deadout' | 'retired' | 'removed';
  type HiveGradingFlag = 'hive_grading_strong' | 'hive_grading_weak' | 'hive_grading_medium';

  interface BeeGrower {
    name: string;
    contactName: string;
    contactPhoneNumber: string;
    contactEmail: string;
  }

  interface BeeGrower {
    name: string;
    contactName: string | null;
    contactPhoneNumber: string | null;
    contactEmail: string | null;
  }

  interface BeeContractFinances {
    costPerHive: number | undefined;
    totalPrice: number | undefined;
    address: string;
    notes: string;
  }

  interface BeeContractOverview {
    name: string;
    cropTypesIds: number[];
    nbRequiredHives: number | undefined;
    beesIn: Date | null;
    beesOut: Date | null;
    archivedAt?: string;
  }

  interface BeeContractForm extends BeeContractOverview, BeeContractFinances {
    grower: BeeGrower;
  }

  interface BeeContract extends BeeContractForm {
    id: number;
    nbBlocks?: number | null;
    nbDrops?: number | null;
    nbTrackedHives?: number | null;
    nbTargetedHives?: number | null;
    totalAcres?: number | null;
    contractYear?: number | null;
  }

  interface BeeContractSummary {
    id: number;
    name: string;
    latest_drop_name: string; // todo: switch to camelcase in redux
    crop_types_ids: number[];
  }

  interface BeeDrop {
    id: number;
    name: string;
    nbHives: number;
    dropCenter: BeePointGeometry;
    targetHiveNumber: number | null;
    requiringAttention: boolean;
    latestFeedings: BeeYardExecutedAction[];
    latestTreatments: BeeYardExecutedAction[];
    lastInspection: BeeYardInspection | null;
    nbStrongHives: number;
    nbMediumHives: number;
    nbWeakHives: number;
    nbDeadoutSeason: number;
    nbQueenlessHives: number;
    status: BeeYardStatus;
  }

  interface BeeYardOnMapInfo {
    id: number;
    name: string;
    status: BeeYardStatus;
    yardCenter: BeePointGeometry;
    geometry: BeePolygonGeometry;
    nbHives: number;
    hivesPosition: Array<BeePointGeometry>;
    lastInspection: BeeYardInspection | null;
    lastInspectionDatetime: string | null;
    contractName?: string;
  }

  interface BeeYardOnMapDetailedInfo extends BeeYardOnMapInfo {
    contractId: string;
    contractName: string;
    cropTypesIds: Array<number>;
    yardType: YardType;
    lastVisits: number | null;
    lastWorkers: Array<string>;
    lastInspection: BeeYardInspection | null;
    lastFeedingDatetime: string;
    lastFeedingAction: number;
    lastTreatmentDatetime: string;
    lastTreatmentAction: number;
    lastActivity: BeeActivityByDate | null;
  }

  interface BeeYardDetails {
    id: number;
    name: string;
    status: BeeYardStatus;
    nbHives: number;
    nbDeadoutSeason: number;
    nbQueenlessHives: number;
    nbQueenrightHives: number;
    isNew: boolean;
    yardType: YardType;
    activeChangeDatetime: string;
    avgVisits: number | null;
    contractId: number | null;
    contractName: string | null;
    createdAt: string;
    deletedAt: string;
    cropTypesIds: Array<number>;
    fieldNotes: string | null;
    fieldNotesUpdatedAt: string | null;
    fieldNotesUpdatedBy: string | null;

    geometry: BeePolygonGeometry;
    yardCenter: BeePointGeometry;
    nearbyYards: Array<{ name: string; geometry: BeePolygonGeometry; yardCenter: BeePointGeometry }>;

    groupIds: Array<number>;
    hivesPosition: Array<string>;
    nearestLocation: string;
    lastFeedingAction: number | null;
    lastFeedingDatetime: string | null;
    lastTreatmentAction: number | null;
    lastTreatmentDatetime: string | null;

    healthScore: BeeHealthScore;
  }

  interface BeeYardExecutedAction {
    actionId: number;
    dates: string[];
  }

  interface BeeYardInspection {
    id: number;
    yardId: number;
    yardName: string;
    analyzer: string;
    analyzedAt: string;
    resolved: boolean;
    resolvedAt: string;
    eventDetails: BeeYardInspectionEventDetails;
  }

  interface BeeYardInspectionEventDetails {
    userId: number;
    reportId: number;
    yardName: string;
    alertType: string;
    inspectionNbHives: number;
    actions: number[];
    note: string;
    yardTotalNbHives: number;
    userName: string;
  }

  interface BeeBlock {
    id: number;
    name: string;
    area: number | null;
    geometry: BeePolygonGeometry;
  }

  interface BeePointOfInterest {
    id: number;
    category: BeePointOfInterestCategory;
    name: string;
    location: BeePointGeometry;
    descriptionEnglish: string | null;
    descriptionSpanish: string | null;
  }

  type BeePointOfInterestCategory = 'access' | 'road' | 'security' | 'storage' | 'hazard';

  interface BeePointGeometry {
    type: 'Point';
    coordinates: [number, number];
  }

  interface BeePolygonGeometry {
    type: 'Polygon';
    coordinates: Array<Array<[number, number]>>;
  }

  type BeeSupportedLangCode = 'en' | 'es';

  interface BeeHiveOnModalDetailedInfo {
    hiveIdentityId: number;
    hiveName: string;
    bornDate: string;
    yardId: number | null;
    yardName: string | null;
    yardContractName: string | null;
    latestTreatmentAction: number | null;
    latestTreatmentDatetime: string | null;
    latestFeedingAction: number | null;
    latestFeedingDatetime: string | null;
    latestGradingAction: number | null;
    latestGradingDatetime: string | null;
    latestStatusAction: number | null;
    latestStatusDatetime: string | null;
    fixingMethodAction: number | null;
    retiredReasonAction: number | null;
    healthScore: number | null;
  }

  interface BeeInspectionDetailPractice {
    categoryId: number;
    actionId?: number;
    value?: number;
  }
  interface BeeActivityRequestHiveIdentity {
    id: number;
    tag: string;
    isNew: boolean;
  }
  interface BeeInspectionDetail {
    hiveIdentities?: Array<{
      id: number;
      tag: string;
      state?: 'deadout' | 'removed' | 'introduced';
    }>;
    practices: Array<BeeInspectionDetailPractice>;
  }
  interface BeeActivityByRequest {
    alertType: BeeActivityByRequestAlertType;
    workerName: string;
    occurredAt: string;
    hiveIdentities?: Array<BeeActivityRequestHiveIdentity>;
    yards?: Array<{
      id: number;
      name: string;
      contractName: string;
      nbHives: number;
      isDeleted: boolean;
    }>;
    inspectionDetails?: Array<BeeInspectionDetail>;
    notes: string | null;
    reportType: 'yard' | 'hive' | 'deadout' | 'yard management';
  }

  type BeeActivityByRequestAlertType =
    | 'new_inspection'
    | 'hive_added_to_yard'
    | 'hive_managed_to_yard'
    | 'lost_hive_marked_as_removed'
    | 'lost_hive_marked_as_deadout'
    | 'yard_cleared_out';

  interface BeeActivityByDate {
    alertType: BeeActivityByDateAlertType;
    workerNames: Array<string>;
    occurredAt: string;
    yardId: number;
    yardName: string;
    yardContractName: string;
    yardIsDeleted: boolean;
    yardNbHives: number;
    yardNbHivesOnVisitDate: number;
    hasYardBeenResized: boolean;
    notes: Array<{ text: string; nbHives: number }>;
    media: Array<{ name: string }>;
    nbHives: number;
    practices: Array<{
      categoryId: number;
      actionId?: number;
      value?: number;
      nbHives: number;
      occurredAt: string;
      /**
       * Basically, if the action was 'Introduced', is it coming from 'Fixed'
       */
      hasFixedHives?: boolean;
    }>;
    editedInformation: Array<string>;
  }

  type BeeActivityByDateAlertType =
    | 'hive_added_to_yard'
    | 'hive_managed_to_yard'
    | 'lost_hive_marked_as_removed'
    | 'lost_hive_marked_as_deadout'
    | 'new_yard_visit'
    | 'yard_edited_by_manager'
    | 'new_yard_created'
    | 'yard_cleared_out'
    | 'yard_deleted';

  interface CrewData {
    name: string;
  }
  interface BeeCrew extends CrewData {
    id: number;
  }
  interface BeeCrewWithMembers extends BeeCrew {
    nbMembers: number;
  }

  interface BeeVisitsCreationData {
    crewId: number;
    description: string;
    yardIds: number[];
    priority: number | null;
    cycleId: number | null;
  }

  interface BeeVisit {
    id: number;
    yard: number;
    priority: number;
    crews: Array<number>;
    completedAt: string | null;
    createdAt: string;
    updatedAt: string | null;
    description: string;
    yardDetails?: BeeGlobalSearchYard;
    cycleId: number | null;
  }

  interface BeeCrewVisitAssignData {
    fromCrewId: number;
    toCrewId?: number;
    newPriority: number;
    visitId: number;
  }

  interface BeeMember {
    id: number;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    status: string;
    lastReport: string | null;
    role: string;
    crewId: number | null;
  }

  type BeeFocusType = 'winterization' | 'splitting' | 'treatment' | 'feeding' | 'supering' | 'honey_harvest';

  type BeeCycleStatusType = 'completed' | 'upcoming' | 'in_progress';

  interface BeeCycle {
    id: number;
    name: string;
    focus: BeeFocusType;
    startsOn: string;
    endsOn: string;
    createdAt: string;
    numberTotalVisits: number;
    numberCompletedVisits: number;
    status: BeeCycleStatusType;
    crews?: BeeCrewWithMembers[];
    nbYards?: number;
    nbHives?: number;
  }

  interface BeeCycleCreationData {
    name: string;
    focus: BeeFocusType;
    startsOn: string;
    endsOn: string;
  }

  interface BeeFocusBranding {
    color: Color;
    icon: string;
    focus: BeeFocusType;
  }

  interface BeeCyclesWithBrandings {
    cycles: Array<BeeCycle>;
    branding: Array<BeeFocusBranding>;
  }
}
