import { FC, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Box } from '@components/common/Box';
import { EmptyView, EmptyViewProps } from '@components/common/EmptyView';
import { Add } from '@components/common/Icon/presets/Add';
import { ArrowDropdown } from '@components/common/Icon/presets/ArrowDropdown.tsx';
import { Loading } from '@components/common/Loading';
import { Menu } from '@components/common/Menu';
import { useDynamicModal } from '@components/common/ModalBase/hooks';
import { Text } from '@components/common/Text';
import { ThreeDotsMenuTrigger } from '@components/common/ThreeDotsMenuTrigger';
import { useDispatch } from '@helpers/Thunk/hooks';
import { useTranslation } from '@hooks/useTranslation';
import { EMPTY_ARRAY } from '@redux/constants';
import { makeFetchCyclesThunk } from '@redux/Planning/actions';

import CycleCell from '../common/CycleCell';

import { CreateOrUpdateCycleModal } from './CreateOrUpdateCycleModal';
import { useSelectedCycle } from './hooks';
import {
  CyclesBorder,
  CyclesHandleLayout,
  CyclesLayout,
  CyclesLayoutInner,
  CyclesTriangleHandle,
  OrderCyclesButtonWrapper,
  SectionLayout,
} from './styles';

type CycleSectionType = 'inProgress' | 'upcoming' | 'completed';

const orderingOptions = [
  {
    title: 'planning:sort_by_creation_date',
    sortCycles: (cycles: BeeCycle[]) =>
      cycles.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
  },
  {
    title: 'planning:sort_by_start_date',
    sortCycles: (cycles: BeeCycle[]) =>
      cycles.sort((a, b) => new Date(a.startsOn).getTime() - new Date(b.startsOn).getTime()),
  },
  {
    title: 'planning:sort_by_end_date',
    sortCycles: (cycles: BeeCycle[]) =>
      cycles.sort((a, b) => new Date(a.endsOn).getTime() - new Date(b.endsOn).getTime()),
  },
];

export const CyclesList: FC = () => {
  const [showList, setShowList] = useState(true);
  const dispatch = useDispatch();
  const t = useTranslation();
  const [sortIndex, setSortIndex] = useState(0);

  const { selectCycle, cycleId } = useSelectedCycle();

  useEffect(() => {
    dispatch(makeFetchCyclesThunk());
  }, [dispatch]);

  const cycles = useSelector((state) => state.planningReducer.visitList.cycles.data ?? EMPTY_ARRAY);
  const { isFetching, didInitialFetch } = useSelector((state) => state.planningReducer.visitList.cycles);

  const menuItems = useMemo(
    () =>
      orderingOptions.map(({ title }, index) => ({
        title: t(title),
        onClick: () => setSortIndex(index),
        checked: index === sortIndex,
      })),
    [t, sortIndex]
  );

  const cycleSections = useMemo(() => {
    const sortedCycles = orderingOptions[sortIndex].sortCycles([...cycles]);
    const inProgress = sortedCycles.filter(({ status }) => status === 'in_progress');
    const upcoming = sortedCycles.filter(({ status }) => status === 'upcoming');
    const completed = sortedCycles.filter(({ status }) => status === 'completed');

    return [
      { type: 'inProgress' as CycleSectionType, name: t('planning:cycles'), cycles: inProgress },
      { type: 'upcoming' as CycleSectionType, name: t('planning:upcoming'), cycles: upcoming },
      { type: 'completed' as CycleSectionType, name: t('planning:completed'), cycles: completed },
    ].filter((section) => section.cycles.length > 0);
  }, [cycles, sortIndex, t]);

  const createCycleModal = useDynamicModal();

  const emptyViewProps = useMemo<EmptyViewProps>(
    () => ({
      heading: t('planning:no_cycles_yet'),
      text: t('planning:no_cycles_message'),
      actionText: t('planning:cycle_create_button'),
      actionIcon: <Add />,
      onActionClick: () => createCycleModal.open(),
    }),
    [t, createCycleModal]
  );

  return (
    <Box>
      <CyclesLayout $isVisible={showList}>
        <CyclesLayoutInner>
          {cycleSections?.length === 0 && !isFetching && didInitialFetch ? (
            <EmptyView {...emptyViewProps} />
          ) : (
            cycleSections?.map((section) => (
              <SectionLayout key={section.name}>
                <Box padding_100>
                  <Text typography={'CaptionSmall'} weight={'600'} color={'contentTertiary'}>
                    {section.name.toUpperCase()}
                  </Text>
                </Box>
                {section.cycles.map((cycle) => (
                  <CycleCell
                    cycle={cycle}
                    selectCycle={() => selectCycle(cycle.id)}
                    key={cycle.id}
                    isSelected={String(cycle.id) === cycleId}
                    showMenu={section.type !== 'completed'}
                    menuId="visits"
                    isSelectable
                    showDates
                  />
                ))}
              </SectionLayout>
            ))
          )}
          <Loading visible={!didInitialFetch || isFetching} blurBackground />
          <CreateOrUpdateCycleModal modalOpenerProps={createCycleModal} />
          <OrderCyclesButtonWrapper fit justifyContent="flex-end">
            <ThreeDotsMenuTrigger id="cycle-order-menu" />
            <Menu items={menuItems} placement="bottom-end" target="cycle-order-menu" />
          </OrderCyclesButtonWrapper>
        </CyclesLayoutInner>
      </CyclesLayout>

      <CyclesHandleLayout $isDrawerOpen={showList}>
        <CyclesBorder $up />
        <CyclesTriangleHandle $isDrawerOpen={showList} onClick={() => setShowList((prev) => !prev)}>
          <ArrowDropdown />
        </CyclesTriangleHandle>
        <CyclesBorder />
      </CyclesHandleLayout>
    </Box>
  );
};
