import styled, { css } from 'styled-components';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';

import { GANTT_CHART_LABEL_WIDTH } from './constants';

export const GanttChartContainer = styled(Box).attrs({
  column: true,
  stretch: true,
})(
  ({ theme }) => css`
    position: relative;
    overflow: hidden;
    background-color: ${theme.colors.surfaceDefault};
    border-radius: ${theme.shape.paperRadius01}px;
    height: 100%;
  `
);

export const GanttChartTitle = styled(Text).attrs({
  typography: 'CaptionSmall',
  color: 'contentTertiary',
  weight: '600',
})(
  ({ theme }) => css`
    width: ${GANTT_CHART_LABEL_WIDTH}px;
    border-right: 1px solid ${theme.colors.borderSecondary};
    align-self: stretch;
    display: flex;
    align-items: center;
    padding-left: ${theme.spacing._100};
  `
);

export const GanttChartHeader = styled(Box).attrs({
  backgroundColor: 'surfaceSecondary',
  alignItems: 'center',
})(
  ({ theme }) => css`
    position: sticky;
    height: 60px;
    border-bottom: 1px solid ${theme.colors.borderSecondary};
  `
);

export const GanttChartContent = styled(Box).attrs({
  fit: true,
})(
  () => css`
    overflow-y: auto;
    overflow-x: hidden;
  `
);

export const GanttChartContentLayout = styled(Box).attrs({
  column: true,
})(
  () => css`
    position: relative;
    align-self: flex-start;
    width: 100%;
  `
);

export const GanttChartRow = styled(Box)(
  ({ theme }) => css`
    height: 90px;
    border-bottom: 1px solid ${theme.colors.borderSecondary};
    border-right: 1px solid ${theme.colors.borderSecondary};
    align-items: center;
  `
);

export const GanttChartLabel = styled(Box).attrs({ paddingTop_0125: true })(
  ({ theme }) => css`
    background-color: ${theme.colors.surfaceSecondary};
    border-right: 1px solid ${theme.colors.borderSecondary};
    width: ${GANTT_CHART_LABEL_WIDTH}px;
    height: 100%;
    position: relative;
  `
);

export const GanttChartBar = styled.div<{
  $color: string;
}>(
  ({ theme, $color }) => css`
    position: relative;
    background-color: ${theme.primitives.colors[$color]};
    border-radius: ${theme.shape.paperRadius01}px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 60px;
  `
);

export const GanttChartDayLabel = styled(Text).attrs({
  typography: 'CaptionSmall',
  color: 'contentTertiary',
})<{ left: number }>(
  ({ left }) => css`
    position: absolute;
    left: ${left}px;
  `
);

export const GanttChartDateLine = styled.div<{ left: number }>(
  ({ theme, left }) => css`
    position: absolute;
    top: 0;
    bottom: 0;
    left: ${left}px;
    width: 2px;
    background-color: ${theme.colors.contentPrimary};
  `
);

export const GanttChartTooltip = styled.div<{ left: number }>(
  ({ theme, left }) => css`
    position: absolute;
    left: ${left - 27}px;
    background-color: ${theme.colors.surfacePrimary};
    color: ${theme.colors.contentPrimaryReverse};
    border-radius: ${theme.shape.paperRadius01}px;
    padding: ${theme.spacing._0125} ${theme.spacing._025};
    box-shadow: ${theme.shadows.boxShadow04};
    white-space: nowrap;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 100%;
      margin-top: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent transparent ${theme.primitives.colors.grey08};
    }
  `
);

export const GanttChartQuarterNavigation = styled(Box).attrs({
  alignItems: 'flex-end',
  gap_100: true,
  marginBottom_100: true,
})(
  ({ theme }) => css`
    padding-left: ${theme.spacing._100};
    white-space: nowrap;
  `
);

export const GanttChartQuarterButton = styled(Box).attrs({
  center: true,
})(
  ({ theme }) => css`
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: ${theme.colors.surfaceSecondary};
    cursor: pointer;

    &:hover {
      background-color: ${theme.colors.surfaceTertiary};
    }
  `
);
