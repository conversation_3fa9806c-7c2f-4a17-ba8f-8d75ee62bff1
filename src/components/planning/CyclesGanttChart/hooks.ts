import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { sum } from 'lodash';

import { useDateUtil } from '@helpers/Date/hooks';
import { useDispatch } from '@helpers/Thunk/hooks';
import { EMPTY_ARRAY } from '@redux/constants';
import { makeFetchCrewsThunk } from '@redux/Crew/actions';
import { makeFetchCyclesThunk } from '@redux/Planning/actions';
import { useSelectedCycleFocuses } from '@redux/Planning/hooks';

import { useOrderedVisits } from '../VisitsByCrewList/hooks';

import { QuarterDirection } from './constants';

interface QuarterDates {
  startDate: Date;
  endDate: Date;
}

/**
 * Hook to manage quarter navigation
 */
export function useQuarterNavigation() {
  const [currentQuarter, setCurrentQuarter] = useState(() => {
    const today = new Date();
    const quarter = Math.floor(today.getMonth() / 3);
    const year = today.getFullYear();
    return { quarter, year };
  });

  // Calculate quarter start and end dates
  const quarterDates = useMemo(() => {
    const { quarter, year } = currentQuarter;
    const startMonth = quarter * 3;
    const endMonth = startMonth + 2;

    const startDate = new Date(year, startMonth, 1);
    const endDate = new Date(year, endMonth + 1, 0); // Last day of the end month

    return { startDate, endDate };
  }, [currentQuarter]);

  // Navigate to a different quarter
  const navigateQuarter = useCallback((direction: QuarterDirection) => {
    setCurrentQuarter((prev) => {
      let { quarter, year } = prev;

      if (direction === QuarterDirection.NEXT) {
        quarter += 1;
        if (quarter > 3) {
          quarter = 0;
          year += 1;
        }
      } else if (direction === QuarterDirection.PREVIOUS) {
        quarter -= 1;
        if (quarter < 0) {
          quarter = 3;
          year -= 1;
        }
      } else {
        // Reset to current quarter
        const today = new Date();
        quarter = Math.floor(today.getMonth() / 3);
        year = today.getFullYear();
      }

      return { quarter, year };
    });
  }, []);

  // Format quarter label
  const quarterLabel = useMemo(() => {
    const { quarter, year } = currentQuarter;
    const quarterNumber = quarter + 1; // 0-based to 1-based
    return `Q${quarterNumber} ${year}`;
  }, [currentQuarter]);

  return {
    quarterDates,
    quarterLabel,
    navigateQuarter,
    currentQuarter,
  };
}

export function useCyclesData(quarterDates: QuarterDates) {
  const dispatch = useDispatch();
  const dateUtil = useDateUtil();

  const visits = useOrderedVisits();
  const cycles = useSelector((state) => state.planningReducer.visitList.cycles.data ?? EMPTY_ARRAY);
  const crews = useSelector((state) => state.crewReducer.crews.data ?? EMPTY_ARRAY);
  const crewsMap = useMemo(() => {
    return crews.reduce((acc, crew) => {
      acc[crew.id] = crew;
      return acc;
    }, {} as Record<number, BeeCrewWithMembers>);
  }, [crews]);
  const { isFetching, didInitialFetch } = useSelector((state) => state.planningReducer.visitList.cycles);
  const focusBranding = useSelector((state) => state.planningReducer.visitList.cycles.focusBranding ?? []);
  const { selectedFocuses } = useSelectedCycleFocuses();

  useEffect(() => {
    dispatch(makeFetchCyclesThunk());
    dispatch(makeFetchCrewsThunk());
  }, [dispatch]);

  const timelineItems = useMemo(() => {
    return cycles
      .filter((cycle) => {
        const startDate = dateUtil.getDateFromFormattedISO(cycle.startsOn);
        const endDate = dateUtil.getDateFromFormattedISO(cycle.endsOn);
        return (
          (!selectedFocuses.length || selectedFocuses.includes(cycle.focus)) &&
          ((startDate <= quarterDates.endDate && endDate >= quarterDates.startDate) ||
            (startDate >= quarterDates.startDate && startDate <= quarterDates.endDate) ||
            (endDate >= quarterDates.startDate && endDate <= quarterDates.endDate))
        );
      })
      .map((cycle) => {
        const filteredVisits = visits.filter((visit) => visit.cycleId === cycle.id);
        const crewsSet = new Set(filteredVisits.map((visit) => visit.crews).flat());
        const yardsSet = new Set(filteredVisits.map((visit) => visit.yard));

        return {
          ...cycle,
          nbHives: sum(filteredVisits.map((visit) => visit.yardDetails?.nbHives)),
          crews: Array.from(crewsSet).map((crewId) => crewsMap[crewId]),
          nbYards: yardsSet.size,
        };
      });
  }, [cycles, dateUtil, quarterDates, selectedFocuses, visits, crewsMap]);

  const focusBrandingByType = useMemo(() => {
    return focusBranding.reduce((acc, branding) => {
      acc[branding.focus] = branding;
      return acc;
    }, {} as Record<BeeFocusType, BeeFocusBranding>);
  }, [focusBranding]);

  return {
    cycles: timelineItems,
    focusBrandingByType,
    isLoading: isFetching,
    didInitialFetch,
  };
}

/**
 * Hook to calculate the number of days in a quarter
 * @param quarterDates Object containing startDate and endDate of the quarter
 * @returns Number of days in the quarter
 */
export function useNumberOfQuarterDays(quarterDates: QuarterDates): number {
  return useMemo(() => {
    const { startDate, endDate } = quarterDates;

    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    // Convert to days (add 1 to include both start and end dates)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

    return diffDays;
  }, [quarterDates]);
}

/**
 * Hook to get the three month labels for a quarter
 * @param quarterDates Object containing startDate and endDate of the quarter
 * @returns Array of month names in the quarter
 */
export function useQuarterMonths(quarterDates: QuarterDates): string[] {
  return useMemo(() => {
    const { startDate } = quarterDates;
    const startMonth = startDate.getMonth();

    const monthFormatter = new Intl.DateTimeFormat('en-US', { month: 'long' });
    const monthLabels: string[] = [];

    for (let i = 0; i < 3; i++) {
      const monthDate = new Date(startDate.getFullYear(), startMonth + i, 1);
      monthLabels.push(monthFormatter.format(monthDate));
    }

    return monthLabels;
  }, [quarterDates]);
}

/**
 * Hook to calculate day offsets from the start of the quarter for the 1st and 15th of each month
 * @param quarterDates Object containing startDate and endDate of the quarter
 * @returns Array of day numbers representing offsets from the start of the quarter
 */
export function useQuarterDayOffsets(quarterDates: QuarterDates): number[] {
  return useMemo(() => {
    const { startDate } = quarterDates;
    const year = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const dayOffsets: number[] = [];

    const msPerDay = 24 * 60 * 60 * 1000;

    // Get the first day of the quarter (to calculate offsets from)
    const quarterStart = new Date(year, startMonth, 1);

    for (let i = 0; i < 3; i++) {
      const currentMonth = startMonth + i;

      // Create Date objects for 1st and 15th of the month
      const firstDay = new Date(year, currentMonth, 1);
      const fifteenthDay = new Date(year, currentMonth, 15);

      // Calculate days from start of quarter
      let dayOffset = Math.floor((firstDay.getTime() - quarterStart.getTime()) / msPerDay);
      dayOffsets.push(dayOffset);

      dayOffset = Math.floor((fifteenthDay.getTime() - quarterStart.getTime()) / msPerDay);
      dayOffsets.push(dayOffset);
    }

    return dayOffsets;
  }, [quarterDates]);
}

export interface CycleDayIndices {
  startDay: number;
  endDay: number;
  cycle: BeeCycle;
}

/**
 * Hook to calculate the start and end day indices for each cycle within a quarter
 * @param cycles List of cycles
 * @param quarterDates Object containing startDate and endDate of the quarter
 * @returns Array of objects with cycleId, startDay, and endDay indices
 */
export function useCycleDayIndices(cycles: BeeCycle[], quarterDates: QuarterDates): CycleDayIndices[] {
  const dateUtil = useDateUtil();

  return useMemo(() => {
    if (!cycles || cycles.length === 0) {
      return [];
    }

    const { startDate, endDate } = quarterDates;
    const quarterStartTime = startDate.getTime();
    const quarterEndTime = endDate.getTime();

    const msPerDay = 24 * 60 * 60 * 1000;

    const totalDaysInQuarter = Math.ceil((quarterEndTime - quarterStartTime) / msPerDay) + 1;

    return cycles.map((cycle) => {
      const cycleStartDate = dateUtil.getDateFromFormattedISO(cycle.startsOn);
      const cycleEndDate = dateUtil.getDateFromFormattedISO(cycle.endsOn);

      let startDay = 0;
      if (cycleStartDate > startDate) {
        startDay = Math.floor((cycleStartDate.getTime() - quarterStartTime) / msPerDay);
      }

      let endDay = totalDaysInQuarter - 1; // Default to last day of quarter
      if (cycleEndDate < endDate) {
        endDay = Math.floor((cycleEndDate.getTime() - quarterStartTime) / msPerDay);
      }

      return {
        startDay,
        endDay,
        cycle,
      };
    });
  }, [cycles, quarterDates, dateUtil]);
}
