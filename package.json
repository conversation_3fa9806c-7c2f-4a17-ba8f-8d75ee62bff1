{"name": "nectar.buzz", "version": "3.29.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "fix": "npx eslint --fix . ", "format": "prettier-eslint --write \"{,!(node_modules)/**/}*.{js,jsx}\"", "icons": "node scripts/icons/import-svg-icons.js", "test": "vitest"}, "dependencies": {"@brainhubeu/react-carousel": "^1.12.6", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@google/markerclusterer": "^2.0.9", "@googlemaps/js-api-loader": "^1.16.6", "@googlemaps/markerclustererplus": "^1.2.10", "@hookform/resolvers": "^3.3.2", "@storybook/addon-actions": "^8.1.1", "@storybook/manager-api": "^8.1.1", "@types/intercom-web": "^2.8.24", "@types/react-gtm-module": "^2.0.1", "@types/react-modal": "^3.13.1", "@types/redux-logger": "^3.0.9", "@types/supercluster": "^7.1.0", "ag-grid-community": "^29.3.5", "ag-grid-react": "^29.3.5", "buffer": "^6.0.3", "city-timezones": "^1.2.1", "d3": "^7.8.4", "eslint-plugin-simple-import-sort": "^8.0.0", "fuse.js": "^6.6.2", "hex-to-css-filter": "^5.4.0", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "i18next": "^15.1.3", "i18next-browser-languagedetector": "^3.0.1", "immer": "^9.0.6", "jspdf": "^3.0.1", "jsts": "2.7.1", "lodash": "^4.17.21", "lodash.difference": "^4.5.0", "lodash.intersection": "^4.4.0", "lottie-web": "^5.10.0", "moment": "^2.30.1", "moment-timezone": "^0.5.43", "prop-types": "^15.7.2", "query-string": "^6.13.8", "react": "^18.2.0", "react-aria": "^3.19.0", "react-calendar": "^3.7.0", "react-device-detect": "^1.17.0", "react-dom": "^18.2.0", "react-grid-system": "^7.1.2", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.49.0", "react-i18next": "^10.0.0", "react-infinite-scroll-component": "^6.1.0", "react-modal": "^3.15.1", "react-number-format": "^4.4.1", "react-redux": "^9.1.1", "react-redux-snackbar": "^1.1.0", "react-router": "^5.3.3", "react-router-dom": "^5.3.3", "react-router-hash-link": "^2.4.0", "react-scripts": "^5.0.1", "react-stately": "^3.17.0", "react-tooltip-currenttarget": "^3.3.5", "react-vis": "^1.11.7", "recharts": "^2.12.7", "redux": "^4.2.0", "redux-devtools-extension": "^2.13.9", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "styled-components": "5.3.11", "supercluster": "^7.1.5", "svg-loaders-react": "^2.1.0", "tippy.js": "^6.3.7", "update-browserslist-db": "^1.0.13", "uuid": "^9.0.1", "vite-plugin-svgr": "^4.2.0", "vitest": "^3.0.8", "wkx": "^0.5.0", "yup": "^1.3.2"}, "devDependencies": {"@chromatic-com/storybook": "^1", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@storybook/addon-essentials": "^8.1.1", "@storybook/addon-interactions": "^8.1.1", "@storybook/addon-links": "^8.1.1", "@storybook/addon-mdx-gfm": "^8.1.1", "@storybook/addon-styling": "^1.3.1", "@storybook/blocks": "^8.1.1", "@storybook/builder-vite": "^8.1.1", "@storybook/preset-create-react-app": "^8.1.1", "@storybook/react": "^8.1.1", "@storybook/react-vite": "^8.1.1", "@storybook/test": "^8.1.1", "@types/d3": "^7.4.0", "@types/google.maps": "^3.50.4", "@types/jest": "^24.0.13", "@types/jsts": "^0.17.14", "@types/lodash": "^4.17.0", "@types/node": "^20.12.7", "@types/react": "^18.2.66", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-calendar": "^3.5.2", "@types/react-dom": "^18.2.22", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.26", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "babel-plugin-named-exports-order": "^0.0.2", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.4.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "eslint-plugin-storybook": "^0.8.0", "nth-check": "2.1.0", "prettier": "^2.7.1", "rollup-plugin-polyfill-node": "^0.13.0", "storybook": "^8.1.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.2.2", "unzipper": "^0.10.11", "util": "^0.12.4", "vite": "^5.4.12", "vite-plugin-checker": "^0.6.4", "webpack": "^5.94.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}